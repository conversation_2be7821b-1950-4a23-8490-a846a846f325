<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Diagnostic Tool</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .result { padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 5px; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px; }
        .step h4 { margin-top: 0; color: #495057; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Supabase Comment System Diagnostic</h1>
        <p>This tool will help diagnose and fix the 401 error in your comment system.</p>

        <div class="test-section">
            <h3>Step 1: Test Supabase Connection</h3>
            <button onclick="testConnection()">Test Connection</button>
            <div id="connection-result"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Check Database Tables</h3>
            <button onclick="checkTables()">Check Tables</button>
            <div id="tables-result"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Anonymous Access</h3>
            <button onclick="testAnonymousAccess()">Test Anonymous Access</button>
            <div id="anonymous-result"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Create Database Tables (if needed)</h3>
            <button onclick="createTables()">Create Tables</button>
            <div id="create-result"></div>
        </div>

        <div class="test-section">
            <h3>Step 5: Test Comment Insertion</h3>
            <button onclick="testInsert()">Test Insert</button>
            <div id="insert-result"></div>
        </div>

        <div class="step">
            <h4>📋 Manual Setup Instructions</h4>
            <p>If the automatic setup doesn't work, you can manually create the tables in your Supabase dashboard:</p>
            <pre>-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  website VARCHAR(255),
  message TEXT NOT NULL,
  post_id VARCHAR(255) NOT NULL DEFAULT 'general',
  parent_id INTEGER REFERENCES comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS for public commenting
ALTER TABLE comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
('Technology', 'Posts about latest technology trends'),
('Web Development', 'Full-stack development tutorials and tips'),
('AI & Machine Learning', 'Artificial Intelligence and ML insights')
ON CONFLICT (name) DO NOTHING;</pre>
        </div>

        <div id="debug-log"></div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        // Your Supabase configuration
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        let supabaseClient;

        // Initialize Supabase
        try {
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            log('✅ Supabase client initialized');
        } catch (error) {
            log('❌ Failed to initialize Supabase client: ' + error.message);
        }

        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div class="result info">[${timestamp}] ${message}</div>`;
            console.log(message);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testConnection() {
            try {
                log('🔍 Testing Supabase connection...');
                showResult('connection-result', 'Testing connection...', 'info');
                
                // Try to access the auth endpoint
                const response = await fetch(`${SUPABASE_URL}/auth/v1/settings`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Connection successful');
                    showResult('connection-result', '✅ Connection successful! Supabase is reachable.', 'success');
                } else {
                    log('❌ Connection failed: ' + response.status);
                    showResult('connection-result', `❌ Connection failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log('❌ Connection error: ' + error.message);
                showResult('connection-result', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function checkTables() {
            try {
                log('🔍 Checking if tables exist...');
                showResult('tables-result', 'Checking tables...', 'info');
                
                // Try to query comments table
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('count', { count: 'exact', head: true });

                if (error) {
                    log('❌ Comments table check failed: ' + error.message);
                    if (error.message.includes('relation "public.comments" does not exist')) {
                        showResult('tables-result', '❌ Comments table does not exist. Please create it using Step 4 or manual setup.', 'error');
                    } else {
                        showResult('tables-result', `❌ Table access error: ${error.message}`, 'error');
                    }
                } else {
                    log('✅ Comments table exists');
                    showResult('tables-result', '✅ Comments table exists and is accessible!', 'success');
                }
            } catch (error) {
                log('❌ Table check error: ' + error.message);
                showResult('tables-result', `❌ Error checking tables: ${error.message}`, 'error');
            }
        }

        async function testAnonymousAccess() {
            try {
                log('🔍 Testing anonymous access...');
                showResult('anonymous-result', 'Testing anonymous access...', 'info');
                
                // Try to access a simple endpoint without authentication
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY
                    }
                });

                if (response.ok || response.status === 404) {
                    log('✅ Anonymous access working');
                    showResult('anonymous-result', '✅ Anonymous access is working!', 'success');
                } else if (response.status === 401) {
                    log('❌ Anonymous access blocked');
                    showResult('anonymous-result', '❌ Anonymous access is blocked. Check your Supabase auth settings.', 'error');
                } else {
                    log('⚠️ Unexpected response: ' + response.status);
                    showResult('anonymous-result', `⚠️ Unexpected response: ${response.status}`, 'warning');
                }
            } catch (error) {
                log('❌ Anonymous access test error: ' + error.message);
                showResult('anonymous-result', `❌ Error testing anonymous access: ${error.message}`, 'error');
            }
        }

        async function createTables() {
            try {
                log('🔧 Attempting to create tables...');
                showResult('create-result', 'Creating tables...', 'info');
                
                // Try to create comments table using RPC or direct SQL
                const { data, error } = await supabaseClient.rpc('exec_sql', {
                    sql: `
                        CREATE TABLE IF NOT EXISTS comments (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            email VARCHAR(255) NOT NULL,
                            website VARCHAR(255),
                            message TEXT NOT NULL,
                            post_id VARCHAR(255) NOT NULL DEFAULT 'general',
                            parent_id INTEGER REFERENCES comments(id) ON DELETE CASCADE,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );
                        ALTER TABLE comments DISABLE ROW LEVEL SECURITY;
                    `
                });

                if (error) {
                    log('❌ Table creation failed: ' + error.message);
                    showResult('create-result', `❌ Automatic table creation failed: ${error.message}<br><br>Please use the manual setup instructions below.`, 'error');
                } else {
                    log('✅ Tables created successfully');
                    showResult('create-result', '✅ Tables created successfully!', 'success');
                }
            } catch (error) {
                log('❌ Table creation error: ' + error.message);
                showResult('create-result', `❌ Error creating tables: ${error.message}<br><br>Please use the manual setup instructions below.`, 'error');
            }
        }

        async function testInsert() {
            try {
                log('🔍 Testing comment insertion...');
                showResult('insert-result', 'Testing comment insertion...', 'info');
                
                const testComment = {
                    name: 'Diagnostic Test',
                    email: '<EMAIL>',
                    message: 'This is a test comment from the diagnostic tool.',
                    post_id: 'diagnostic-test'
                };

                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (error) {
                    log('❌ Comment insertion failed: ' + error.message);
                    showResult('insert-result', `❌ Comment insertion failed: ${error.message}`, 'error');
                } else {
                    log('✅ Comment inserted successfully');
                    showResult('insert-result', `✅ Comment inserted successfully! ID: ${data[0].id}`, 'success');
                }
            } catch (error) {
                log('❌ Insert test error: ' + error.message);
                showResult('insert-result', `❌ Error testing insert: ${error.message}`, 'error');
            }
        }

        // Auto-run connection test on load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
