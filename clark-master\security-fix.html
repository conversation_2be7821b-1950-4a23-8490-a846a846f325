<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Fix - Enable Proper RLS</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .warning-box { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .success-box { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .info-box { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .sql-code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #dee2e6; white-space: pre-wrap; }
        button { background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 5px; font-size: 14px; }
        button:hover { background: #218838; }
        button.test { background: #007bff; }
        button.test:hover { background: #0056b3; }
        .step { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745; }
        .step h3 { margin-top: 0; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security Fix: Enable Proper Row Level Security</h1>
        
        <div class="warning-box">
            <h3>⚠️ Current Issue</h3>
            <p><strong>Supabase Warning:</strong> "Row Level Security is disabled. Your table is publicly readable and writable."</p>
            <p><strong>Risk:</strong> Anyone can read, modify, or delete any data in your tables.</p>
            <p><strong>Solution:</strong> Enable RLS with proper policies for public commenting.</p>
        </div>

        <div class="step">
            <h3>🔧 Step 1: Enable RLS with Secure Policies</h3>
            <p>Run this SQL in your Supabase Dashboard → SQL Editor:</p>
            <div class="sql-code">-- Enable Row Level Security
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create secure policies for comments table
-- Allow everyone to read comments
CREATE POLICY "Allow public read access" ON comments
    FOR SELECT USING (true);

-- Allow anonymous users to insert comments (but not update/delete)
CREATE POLICY "Allow anonymous insert" ON comments
    FOR INSERT WITH CHECK (true);

-- Allow users to update/delete only their own comments (optional)
-- CREATE POLICY "Allow users to update own comments" ON comments
--     FOR UPDATE USING (auth.email() = email);

-- Create secure policies for categories table
-- Allow everyone to read categories
CREATE POLICY "Allow public read categories" ON categories
    FOR SELECT USING (true);

-- Only authenticated users can modify categories (optional)
-- CREATE POLICY "Allow authenticated users to manage categories" ON categories
--     FOR ALL TO authenticated USING (true);

-- Verify policies are created
SELECT 'Security policies created successfully!' as status;</div>
            <p><strong>This setup provides:</strong></p>
            <ul>
                <li>✅ Public can read all comments and categories</li>
                <li>✅ Anonymous users can post new comments</li>
                <li>❌ Anonymous users cannot edit/delete existing comments</li>
                <li>🔒 Secure and appropriate for a public blog</li>
            </ul>
        </div>

        <div class="step">
            <h3>🧪 Step 2: Test the Secure Setup</h3>
            <button onclick="testSecureSetup()" class="test">Test Comment System with RLS</button>
            <div id="test-result"></div>
        </div>

        <div class="step">
            <h3>✅ Step 3: Verify Security</h3>
            <button onclick="verifyRLS()" class="test">Verify RLS is Working</button>
            <div id="rls-result"></div>
        </div>

        <div class="info-box">
            <h3>🛡️ Security Benefits</h3>
            <p>After applying these policies:</p>
            <ul>
                <li><strong>Read Access:</strong> Anyone can view comments (good for public blog)</li>
                <li><strong>Write Access:</strong> Only new comments can be added anonymously</li>
                <li><strong>Edit Protection:</strong> Existing comments cannot be modified by anonymous users</li>
                <li><strong>Delete Protection:</strong> Comments cannot be deleted by anonymous users</li>
                <li><strong>No More Warnings:</strong> Supabase will show your tables are properly secured</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>🎉 Final Result</h3>
            <p>Your comment system will:</p>
            <ul>
                <li>✅ Work exactly the same for users</li>
                <li>✅ Be properly secured with RLS</li>
                <li>✅ Remove Supabase security warnings</li>
                <li>✅ Follow security best practices</li>
            </ul>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        const supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        async function testSecureSetup() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div style="background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;">Testing secure comment system...</div>';

            try {
                // Test 1: Read comments (should work)
                console.log('Testing read access...');
                const { data: readData, error: readError } = await supabaseClient
                    .from('comments')
                    .select('*')
                    .limit(1);

                if (readError) {
                    resultDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                            <h4>❌ Read Test Failed</h4>
                            <p><strong>Error:</strong> ${readError.message}</p>
                            <p>Please run the SQL code above first.</p>
                        </div>
                    `;
                    return;
                }

                // Test 2: Insert comment (should work)
                console.log('Testing insert access...');
                const testComment = {
                    name: 'Security Test User',
                    email: '<EMAIL>',
                    message: 'Testing secure RLS policies - this comment should post successfully!',
                    post_id: 'security-test'
                };

                const { data: insertData, error: insertError } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (insertError) {
                    resultDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                            <h4>❌ Insert Test Failed</h4>
                            <p><strong>Error:</strong> ${insertError.message}</p>
                            <p><strong>Code:</strong> ${insertError.code}</p>
                            <p>Please run the SQL code above to create proper RLS policies.</p>
                        </div>
                    `;
                    return;
                }

                // Success!
                resultDiv.innerHTML = `
                    <div style="background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;">
                        <h4>✅ Secure Setup Test Successful!</h4>
                        <p><strong>Read Access:</strong> ✅ Working</p>
                        <p><strong>Insert Access:</strong> ✅ Working</p>
                        <p><strong>Comment ID:</strong> ${insertData[0].id}</p>
                        <p><strong>Status:</strong> Your comment system is now secure and functional!</p>
                    </div>
                `;

            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                        <h4>❌ Test Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function verifyRLS() {
            const resultDiv = document.getElementById('rls-result');
            resultDiv.innerHTML = '<div style="background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;">Verifying RLS status...</div>';

            try {
                // Try to read comments - this should work with proper RLS
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('count', { count: 'exact', head: true });

                if (error) {
                    if (error.code === '42501') {
                        resultDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                                <h4>❌ RLS Too Restrictive</h4>
                                <p>RLS is enabled but blocking access. Please run the SQL code above to create proper policies.</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                                <h4>❌ RLS Verification Failed</h4>
                                <p><strong>Error:</strong> ${error.message}</p>
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div style="background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;">
                            <h4>✅ RLS Properly Configured!</h4>
                            <p><strong>Status:</strong> Row Level Security is enabled with proper policies</p>
                            <p><strong>Security:</strong> Your tables are secure but publicly accessible for commenting</p>
                            <p><strong>Comments Count:</strong> ${data.count || 0}</p>
                            <p>🎉 No more security warnings from Supabase!</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;">
                        <h4>❌ Verification Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
