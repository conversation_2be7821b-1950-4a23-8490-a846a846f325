<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Clark - Free Bootstrap 4 Template by Colorlib</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <link
      href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900"
      rel="stylesheet"
    />

    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css" />
    <link rel="stylesheet" href="css/animate.css" />

    <link rel="stylesheet" href="css/owl.carousel.min.css" />
    <link rel="stylesheet" href="css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="css/magnific-popup.css" />

    <link rel="stylesheet" href="css/aos.css" />

    <link rel="stylesheet" href="css/ionicons.min.css" />

    <link rel="stylesheet" href="css/flaticon.css" />
    <link rel="stylesheet" href="css/icomoon.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body data-spy="scroll" data-target=".site-navbar-target" data-offset="300">
    <nav
      class="navbar navbar-expand-lg navbar-dark ftco_navbar ftco-navbar-light site-navbar-target"
      id="ftco-navbar"
    >
      <div class="container">
        <a class="navbar-brand" href="index.html">Clark</a>
        <button
          class="navbar-toggler js-fh5co-nav-toggle fh5co-nav-toggle"
          type="button"
          data-toggle="collapse"
          data-target="#ftco-nav"
          aria-controls="ftco-nav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="oi oi-menu"></span> Menu
        </button>

        <div class="collapse navbar-collapse" id="ftco-nav">
          <ul class="navbar-nav nav ml-auto">
            <li class="nav-item">
              <a href="index.html#home-section" class="nav-link"
                ><span>Home</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#about-section" class="nav-link"
                ><span>About</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#resume-section" class="nav-link"
                ><span>Resume</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#services-section" class="nav-link"
                ><span>Services</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#skills-section" class="nav-link"
                ><span>Skills</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#projects-section" class="nav-link"
                ><span>Projects</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#blog-section" class="nav-link"
                ><span>My Blog</span></a
              >
            </li>
            <li class="nav-item">
              <a href="index.html#contact-section" class="nav-link"
                ><span>Contact</span></a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>
    <!-- Hero-wrap section with breadcrumbs (using the one with the corrected blog link) -->
    <section
      class="hero-wrap js-fullheight"

      data-stellar-background-ratio="0.5"
    >
      <div class="overlay"></div>
      <div class="container">
        <div
          class="row no-gutters slider-text js-fullheight align-items-end justify-content-center"
        >
          <div class="col-md-12 ftco-animate pb-5 mb-3 text-center">
            <h1 class="mb-3 bread">Blog Post</h1>
            <p class="breadcrumbs">
              <span class="mr-2"
                ><a href="index.html"
                  >Home <i class="ion-ios-arrow-forward"></i></a
              ></span>
              <span class="mr-2"
                ><a href="index.html#blog-section"
                  >Blog <i class="ion-ios-arrow-forward"></i></a
              ></span>
              <span>Blog Post <i class="ion-ios-arrow-forward"></i></span>
            </p>
          </div>
        </div>
      </div>
    </section>
    <!-- End of Hero-wrap section -->

    <section class="ftco-section">
      <div class="container">
        <div class="row">
          <div class="col-lg-8 ftco-animate">
            <h2 class="mb-3">
              #1.The Rise of Serverless Architecture
            </h2>
            <p>
              Serverless architecture has been gaining significant traction in the web development world, and for good reason. It shifts the focus from managing servers to building and deploying applications. As a full-stack developer, I've seen firsthand the benefits, including reduced operational overhead, automatic scaling, and cost efficiency.
            </p>
            <p>
              <img src="images/image1.jpg" alt="" class="img-fluid" />
            </p>
            <p>
              By leveraging services like AWS Lambda, Azure Functions, or Google Cloud Functions, developers can concentrate on writing code, leaving the infrastructure management to the cloud providers. This paradigm shift allows for faster development cycles and greater agility, making it an exciting area to explore for any tech enthusiast.
            </p>
            <h2 class="mb-3 mt-5">#2. Demystifying Machine Learning for Web Developers</h2>
            <p>
              The term "Machine Learning" can sound intimidating, but at its core, it's about enabling computers to learn from data without explicit programming. As a full-stack developer, understanding the basics of ML can significantly enhance your ability to build intelligent applications.
            </p>
            <p>
              <img src="images/image2.jpg" alt="" class="img-fluid" />
            </p>
            <p>
              Imagine integrating features like personalized recommendations, automated data analysis, or even predictive capabilities into your web projects. Libraries and frameworks like TensorFlow.js and scikit-learn provide accessible entry points for web developers to experiment with ML concepts and build practical applications. It's a journey of continuous learning, and the potential is immense.
            </p>

          </p>
          <h2 class="mb-3 mt-5">#3. The Synergy Between AI and Full-Stack Development</h2>
          <p>
            The intersection of Artificial Intelligence (AI) and full-stack development is creating exciting new possibilities. As a full-stack developer, having an understanding of AI principles allows you to build more sophisticated and user-centric applications. On the front-end, AI can power features like intelligent chatbots and personalized user interfaces.
          </p>
          <p>
            <img src="images/image3.jpg" alt="" class="img-fluid" />
          </p>
          <p>
            On the back-end, it can drive data analysis, automate tasks, and improve system performance. The ability to bridge the gap between front-end user experience, back-end infrastructure, and AI-driven intelligence is becoming increasingly valuable. Exploring this synergy is key for staying at the forefront of web development innovation.
          </p>

            <div class="tag-widget post-tag-container mb-5 mt-5">
              <div class="tagcloud">
                <a href="#" class="tag-cloud-link">Life</a>
                <a href="#" class="tag-cloud-link">Sport</a>
                <a href="#" class="tag-cloud-link">Tech</a>

              </div>
            </div>

            <div class="about-author d-flex p-4 bg-dark">
              <div class="bio mr-5">
                <img
                  src="images/kb1.png"
                  alt="Image placeholder"
                  class="img-fluid mb-4"
                />
              </div>
              <div class="desc">
                <h3>Insights from kbics</h3>
                <p>
                Full-Stack Developer passionate about all things tech and AI.
                </p>
              </div>
            </div>

            <div class="pt-5 mt-5">
              <h3 class="mb-5" id="commentCountHeading">Comments</h3>
              <ul class="comment-list" id="commentList">
                <!-- Comments will be loaded here dynamically -->
              </ul>
              <!-- END comment-list -->

              <div class="comment-form-wrap pt-5">
                <h3 class="mb-5">Leave a comment</h3>
                <!--
                  The 'action' attribute is not strictly needed when handling submission with JavaScript.
                  Added id="commentForm" for JavaScript targeting.
                -->
                <form id="commentForm" class="p-5 bg-dark">
                  <div class="form-group">
                    <label for="name">Name *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="name"
                      name="name"
                      required
                    />
                  </div>
                  <div class="form-group">
                    <label for="email">Email *</label>
                    <input
                      type="email"
                      class="form-control"
                      id="email"
                      name="email"
                      required
                    />
                  </div>
                  <div class="form-group">
                    <label for="website">Website</label>
                    <input
                      type="url"
                      class="form-control"
                      id="website"
                      name="website"
                    />
                  </div>

                  <div class="form-group">
                    <label for="message">Message</label>
                    <textarea
                      name="message"
                      id="message"
                      cols="30"
                      rows="10"
                      class="form-control"
                      required
                    ></textarea>
                  </div>
                  <div class="form-group">
                    <input
                      type="submit"
                      value="Post Comment"
                      class="btn py-3 px-4 btn-primary"
                    />
                  </div>
                </form>
              </div>
            </div>
          </div>
          <!-- .col-md-8 -->
          <div class="col-lg-4 sidebar ftco-animate">
            <div class="sidebar-box">
              <form action="#" class="search-form" id="commentSearchForm">
                <div class="form-group">
                  <span class="icon icon-search"></span>
                  <input
                    type="text"
                    class="form-control"
                    placeholder="Type a keyword and hit enter"
                    id="commentSearchInput"
                  />
                </div>
              </form>
            </div>
            <div class="sidebar-box ftco-animate">
              <h3 class="heading-sidebar">Categories</h3>
              <ul class="categories" id="categoryList">
                <!-- Categories will be loaded here dynamically -->
              </ul>
            </div>

            <div class="sidebar-box ftco-animate">
              <h3 class="heading-sidebar">Recent Blog</h3>
              <div class="block-21 mb-4 d-flex">
                <a
                  class="blog-img mr-4"
                  style="background-image: url(images/image1.jpg)"
                ></a>
                <div class="text">
                  <h3 class="heading">
                    <a href="#"
                      >The Rise of Serverless Architecture

                      </a
                    >
                  </h3>
                  <div class="meta">
                    <div>
                      <a href="#"
                        ><span class="icon-calendar"></span> March 12, 2019</a
                      >
                    </div>
                    <div>
                      <a href="#"><span class="icon-person"></span> Admin</a>
                    </div>
                    <div>
                      <a href="#"><span class="icon-chat"></span> 19</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="block-21 mb-4 d-flex">
                <a
                  class="blog-img mr-4"
                  style="background-image: url(images/image2.jpg)"
                ></a>
                <div class="text">
                  <h3 class="heading">
                    <a href="#"
                      >Demystifying Machine Learning for Web Developers</a
                    >
                  </h3>
                  <div class="meta">
                    <div>
                      <a href="#"
                        ><span class="icon-calendar"></span> March 12, 2019</a
                      >
                    </div>
                    <div>
                      <a href="#"><span class="icon-person"></span> Admin</a>
                    </div>
                    <div>
                      <a href="#"><span class="icon-chat"></span> 19</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="block-21 mb-4 d-flex">
                <a
                  class="blog-img mr-4"
                  style="background-image: url(images/image3.jpg)"
                ></a>
                <div class="text">
                  <h3 class="heading">
                    <a href="#"
                      >The Synergy Between AI and Full-Stack Development</a
                    >
                  </h3>
                  <div class="meta">
                    <div>
                      <a href="#"
                        ><span class="icon-calendar"></span> March 12, 2019</a
                      >
                    </div>
                    <div>
                      <a href="#"><span class="icon-person"></span> Admin</a>
                    </div>
                    <div>
                      <a href="#"><span class="icon-chat"></span> 19</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="sidebar-box ftco-animate">
              <h3 class="heading-sidebar">Tag Cloud</h3>
              <div class="tagcloud">
                <a href="#" class="tag-cloud-link">Machine Learning</a>
                <a href="#" class="tag-cloud-link">Neural Networks</a>
                <a href="#" class="tag-cloud-link">Data</a>
                <a href="#" class="tag-cloud-link">APIs</a>
                <a href="#" class="tag-cloud-link">Web AI</a>
                <a href="#" class="tag-cloud-link">NLP</a>
                <a href="#" class="tag-cloud-link"> Cloud AI</a>
                <a href="#" class="tag-cloud-link">Predictive Analytics</a>
              </div>
            </div>

            <div class="sidebar-box ftco-animate">
              <h3 class="heading-sidebar">What to Write</h3>
              <p>
                Dive into cutting-edge tech and AI topics from a full-stack developer's view. Get fresh insights and practical tips
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- .section -->
    <footer class="ftco-footer ftco-section">
      <div class="container">
        <div class="row mb-5">
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">About</h2>
              <p>
                Full-stack developer focused on clean code, modern tech, and user-friendly, reliable applications.
              </p>
              <ul
                class="ftco-footer-social list-unstyled float-md-left float-lft mt-5"
              >
                <li class="ftco-animate">
                  <a href="#"><span class="icon-twitter"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="#"><span class="icon-facebook"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="#"><span class="icon-instagram"></span></a>
                </li>

                <li class="ftco-animate">
                  <a href="#"><span class="icon-linkedin"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="https://github.com/KibruMichael" target="_blank"><span class="icon-github"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="https://t.me/+2gQlKrJVVE1kZDFk" target="_blank"><span class="icon-telegram"></span></a>
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4 ml-md-4">
              <h2 class="ftco-heading-2">Links</h2>
              <ul class="list-unstyled">
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Home</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>About</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Services</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Projects</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Contact</a
                  >
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Services</h2>
              <ul class="list-unstyled">
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>UI/UX Design</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Full-Stack Development</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Dashboard Development</a
                  >
                </li>

              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Have a Questions?</h2>
              <div class="block-23 mb-3">
                <ul>
                  <li>
                    <a href="mailto:<EMAIL>"
                      ><span class="icon icon-envelope"></span
                      ><span class="text"><EMAIL></span></a
                    >
                  </li>
                  <li>
                    <a href="#"
                      ><span class="icon icon-phone"></span
                      ><span class="text">****** 3929 210</span></a
                    >
                  </li>

                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- loader -->
    <div id="ftco-loader" class="show fullscreen">
      <svg class="circular" width="48px" height="48px">
        <circle
          class="path-bg"
          cx="24"
          cy="24"
          r="22"
          fill="none"
          stroke-width="4"
          stroke="#eeeeee"
        />
        <circle
          class="path"
          cx="24"
          cy="24"
          r="22"
          fill="none"
          stroke-width="4"
          stroke-miterlimit="10"
          stroke="#F96D00"
        />
      </svg>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script>
      // Suppress AOS.js deprecation warnings
      const originalConsoleWarn = console.warn;
      console.warn = function(message) {
        if (typeof message === 'string' && message.includes('DOMNodeRemoved')) {
          return; // Suppress AOS.js deprecation warnings
        }
        originalConsoleWarn.apply(console, arguments);
      };
    </script>
    <script src="js/aos.js"></script>
    <script>
      // Restore original console.warn after AOS loads
      setTimeout(() => {
        console.warn = originalConsoleWarn;
      }, 1000);
    </script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/main.js"></script>

    <!-- CUSTOM SCRIPT FOR SUPABASE COMMENT FORM -->
    <script>
      // Global variables for Supabase
      const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
      const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';
      let supabaseClient;

      window.onload = function () {
        // --- Supabase Client Initialization ---

        // Initialize Supabase client
        try {
          console.log("Initializing Supabase client...");
          console.log("window.supabase available:", typeof window.supabase);

          if (window.globalSupabaseClient) {
            console.log("Using existing Supabase client");
            supabaseClient = window.globalSupabaseClient;
          } else if (typeof window.supabase !== 'undefined' && window.supabase.createClient) {
            console.log("Creating new Supabase client");
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            window.globalSupabaseClient = supabaseClient;
            console.log("Supabase client created successfully");
          } else {
            console.error("Supabase library not available");
            alert("Comment system unavailable - Supabase library not loaded");
            return;
          }
        } catch (error) {
          console.error("Failed to initialize Supabase client:", error);
          alert("Comment system unavailable - initialization failed");
          return;
        }

        // Verify client is available
        if (!supabaseClient) {
          console.error("Supabase client is not available");
          return;
        }

        const commentFormElement = document.getElementById("commentForm");
        const commentCountHeading = document.getElementById(
          "commentCountHeading"
        );
        const commentListElement = document.getElementById("commentList");
        const commentSearchForm = document.getElementById("commentSearchForm");
        const commentSearchInput =
          document.getElementById("commentSearchInput");

        // Get post_id from URL (e.g., single.html?post=my-first-post)
        const urlParams = new URLSearchParams(window.location.search);
        const postId = urlParams.get("post") || "general"; // Default to 'general' if no post_id

        // Function to fetch and render comments
        async function fetchAndRenderComments(searchTerm = "") {
          console.log("Fetching comments with searchTerm:", searchTerm); // Debug log

          // Verify supabaseClient is available
          if (!supabaseClient) {
            console.error("Supabase client is not initialized");
            commentListElement.innerHTML = '<li class="text-center"><div style="color: #ff6b6b; padding: 20px;">Error: Database connection not available</div></li>';
            return;
          }

          if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
            console.warn(
              "Supabase URL or Anon Key is not set. Cannot fetch comments."
            );
            return;
          }

          // Show loading indicator
          commentListElement.innerHTML = '<li class="text-center"><div style="color: #ffbd39; padding: 20px;"><i class="icon-refresh" style="animation: spin 1s linear infinite;"></i> Loading comments...</div></li>';

          // Add CSS for loading animation
          if (!document.getElementById('loading-styles')) {
            const style = document.createElement('style');
            style.id = 'loading-styles';
            style.textContent = `
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `;
            document.head.appendChild(style);
          }

          try {
            console.log("Fetching comments for post_id:", postId);
            let query = supabaseClient
              .from("comments")
              .select("*", { count: "exact" })
              .eq("post_id", postId); // Filter by post_id

            if (searchTerm) {
              // Case-insensitive search on name and message
              query = query.or(
                `name.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`
              );
              console.log(
                "Applying search filter:",
                `name.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`
              ); // Debug log
            }

            const {
              data: comments,
              count,
              error,
            } = await query.order("created_at", { ascending: true }); // Order by creation time

            console.log("Fetched comments:", comments); // Debug log
            console.log("Comment count:", count); // Debug log

            if (error) {
              console.error("Error fetching comments:", error);
              commentCountHeading.textContent = "Comments (Error loading)";
              return;
            }

            // Update comment count
            commentCountHeading.textContent = `${count} Comments`;

            // Clear existing comments
            commentListElement.innerHTML = "";

            // Check if there are no comments
            if (comments.length === 0) {
              commentListElement.innerHTML = '<li class="text-center"><div style="color: rgba(255,255,255,0.7); padding: 40px; font-style: italic;">No comments yet. Be the first to share your thoughts!</div></li>';
              return;
            }

            // Organize comments into a hierarchical structure
            const commentMap = new Map();
            comments.forEach((comment) => {
              comment.children = []; // Initialize children array
              commentMap.set(comment.id, comment);
            });

            comments.forEach((comment) => {
              if (comment.parent_id) {
                const parent = commentMap.get(comment.parent_id);
                if (parent) {
                  parent.children.push(comment);
                }
              }
            });

            // Filter out replies to get only top-level comments
            const topLevelComments = comments.filter(
              (comment) => !comment.parent_id
            );

            // Function to render a single comment and its children
            function renderComment(comment, isChild = false) {
              const commentDate = new Date(comment.created_at).toLocaleString();
              const commentClass = isChild
                ? "comment comment-reply"
                : "comment";
              const commentElement = document.createElement("li");
              commentElement.className = commentClass;
              commentElement.innerHTML = `
                <div class="vcard bio">
                  <img src="images/kb1.png" alt="Image placeholder" />
                </div>
                <div class="comment-body">
                  <h3>${comment.name}</h3>
                  <div class="meta">${commentDate}</div>
                  <p>${comment.message}</p>
                  <p><a href="#" class="reply" data-comment-id="${comment.id}">Reply</a></p>
                </div>
              `;

              // Append reply form placeholder
              const replyFormPlaceholder = document.createElement("div");
              replyFormPlaceholder.id = `reply-form-for-${comment.id}`;
              replyFormPlaceholder.className = "reply-form-placeholder";
              commentElement
                .querySelector(".comment-body")
                .appendChild(replyFormPlaceholder);

              if (comment.children.length > 0) {
                const childrenList = document.createElement("ul");
                childrenList.className = "children";
                comment.children.forEach((child) => {
                  childrenList.appendChild(renderComment(child, true));
                });
                commentElement.appendChild(childrenList);
              }
              return commentElement;
            }

            // Render top-level comments
            topLevelComments.forEach((comment) => {
              commentListElement.appendChild(renderComment(comment));
            });

            // Add event listeners for reply buttons
            document.querySelectorAll(".reply").forEach((button) => {
              button.addEventListener("click", function (event) {
                event.preventDefault();
                const parentCommentId = this.dataset.commentId;
                const replyFormPlaceholder = document.getElementById(
                  `reply-form-for-${parentCommentId}`
                );

                // Hide any existing reply forms
                document
                  .querySelectorAll(".reply-form-wrap")
                  .forEach((form) => form.remove());

                // Create and append a new reply form
                const replyFormWrap = document.createElement("div");
                replyFormWrap.className =
                  "comment-form-wrap pt-5 reply-form-wrap";
                replyFormWrap.innerHTML = `
                  <h3 class="mb-5">Reply to Comment</h3>
                  <form class="p-5 bg-dark reply-form" data-parent-id="${parentCommentId}">
                    <div class="form-group">
                      <label for="reply-name">Name *</label>
                      <input type="text" class="form-control" id="reply-name" name="name" required />
                    </div>
                    <div class="form-group">
                      <label for="reply-email">Email *</label>
                      <input type="email" class="form-control" id="reply-email" name="email" required />
                    </div>
                    <div class="form-group">
                      <label for="reply-website">Website</label>
                      <input type="url" class="form-control" id="reply-website" name="website" />
                    </div>
                    <div class="form-group">
                      <label for="reply-message">Message</label>
                      <textarea name="message" id="reply-message" cols="30" rows="5" class="form-control" required></textarea>
                    </div>
                    <div class="form-group">
                      <input type="submit" value="Post Reply" class="btn py-3 px-4 btn-primary" />
                    </div>
                  </form>
                `;
                replyFormPlaceholder.appendChild(replyFormWrap);

                // Scroll to the reply form
                replyFormWrap.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });

                // Add submit listener for the new reply form
                replyFormWrap
                  .querySelector(".reply-form")
                  .addEventListener("submit", async function (replyEvent) {
                    replyEvent.preventDefault();

                    const replyName = document
                      .getElementById("reply-name")
                      .value.trim();
                    const replyEmail = document
                      .getElementById("reply-email")
                      .value.trim();
                    const replyWebsite = document
                      .getElementById("reply-website")
                      .value.trim();
                    const replyMessage = document
                      .getElementById("reply-message")
                      .value.trim();
                    const replyParentId = this.dataset.parentId;

                    const replySubmitButton = this.querySelector(
                      'input[type="submit"]'
                    );
                    const originalReplyButtonText = replySubmitButton.value;
                    replySubmitButton.value = "Posting Reply...";
                    replySubmitButton.disabled = true;

                    try {
                      // Verify supabaseClient is available
                      if (!supabaseClient) {
                        throw new Error("Supabase client is not initialized");
                      }

                      const { data, error } = await supabaseClient
                        .from("comments")
                        .insert([
                          {
                            name: replyName,
                            email: replyEmail,
                            website: replyWebsite || null,
                            message: replyMessage,
                            post_id: postId,
                            parent_id: replyParentId,
                          },
                        ])
                        .select();

                      if (error) {
                        console.error("Error posting reply:", error);
                        alert(
                          "Sorry, there was an error posting your reply: " +
                            error.message
                        );
                      } else {
                        // Show success message for reply
                        const replySuccessMessage = document.createElement("div");
                        replySuccessMessage.className = "alert alert-success mt-3";
                        replySuccessMessage.innerHTML = "✅ Reply posted successfully!";
                        replySuccessMessage.style.cssText = "background-color: #28a745; color: white; padding: 10px; border-radius: 5px; margin-top: 10px;";
                        replyFormWrap.appendChild(replySuccessMessage);

                        // Remove reply form after showing success message
                        setTimeout(() => {
                          replyFormWrap.remove();
                        }, 2000);

                        await fetchAndRenderComments(); // Re-fetch and render all comments
                      }
                    } catch (error) {
                      console.error(
                        "An unexpected error occurred while posting reply:",
                        error
                      );
                      alert("An unexpected error occurred. Please try again.");
                    } finally {
                      replySubmitButton.value = originalReplyButtonText;
                      replySubmitButton.disabled = false;
                    }
                  });
              });
            });
          } catch (error) {
            console.error(
              "An unexpected error occurred while fetching comments:",
              error
            );
            commentCountHeading.textContent = "Comments (Error)";
          }
        }

        // --- Comment Form Submission Handler ---
        if (commentFormElement) {
          commentFormElement.addEventListener("submit", async function (event) {
            event.preventDefault(); // Prevent default browser form submission

            const nameInput = document.getElementById("name");
            const emailInput = document.getElementById("email");
            const websiteInput = document.getElementById("website");
            const messageInput = document.getElementById("message");

            const name = nameInput.value.trim();
            const email = emailInput.value.trim();
            const website = websiteInput.value.trim(); // Optional field
            const message = messageInput.value.trim();

            // Enhanced validation
            if (!name || name.length < 2) {
              alert("Please enter a valid name (at least 2 characters).");
              nameInput.focus();
              return;
            }

            if (!email || !email.includes('@')) {
              alert("Please enter a valid email address.");
              emailInput.focus();
              return;
            }

            if (!message || message.length < 10) {
              alert("Please enter a message with at least 10 characters.");
              messageInput.focus();
              return;
            }

            const submitButton = commentFormElement.querySelector(
              'input[type="submit"]'
            );
            const originalButtonText = submitButton.value;
            submitButton.value = "Posting...";
            submitButton.disabled = true;

            try {
              // Verify supabaseClient is available
              if (!supabaseClient) {
                throw new Error("Supabase client is not initialized");
              }

              console.log("Submitting comment for post_id:", postId);
              const commentData = {
                name: name,
                email: email,
                website: website || null,
                message: message,
                post_id: postId,
              };
              console.log("Comment data:", commentData);

              const { data, error } = await supabaseClient
                .from("comments")
                .insert([commentData])
                .select();

              console.log("Insert response:", { data, error });

              if (error) {
                console.error("Error posting comment:", error);
                alert(
                  "Sorry, there was an error posting your comment: " +
                    error.message
                );
              } else {
                // Show success message with better UX
                const successMessage = document.createElement("div");
                successMessage.className = "alert alert-success mt-3";
                successMessage.innerHTML = "✅ Comment posted successfully!";
                successMessage.style.cssText = "background-color: #28a745; color: white; padding: 10px; border-radius: 5px; margin-top: 10px;";
                commentFormElement.appendChild(successMessage);

                // Remove success message after 3 seconds
                setTimeout(() => {
                  if (successMessage.parentNode) {
                    successMessage.parentNode.removeChild(successMessage);
                  }
                }, 3000);

                commentFormElement.reset(); // Clear the form fields
                await fetchAndRenderComments(); // Re-fetch and render comments after successful post
              }
            } catch (error) {
              console.error("An unexpected error occurred:", error);
              alert("An unexpected error occurred. Please try again.");
            } finally {
              submitButton.value = originalButtonText;
              submitButton.disabled = false;
            }
          });
        } else {
          console.warn(
            'Comment form with id "commentForm" not found in single.html.'
          );
        }

        // Initial fetch and render of comments when the page loads
        fetchAndRenderComments();

        // Add event listener for search form submission
        if (commentSearchForm) {
          commentSearchForm.addEventListener("submit", async function (event) {
            event.preventDefault(); // Prevent default form submission
            const searchTerm = commentSearchInput.value.trim();
            console.log("Search form submitted. Search term:", searchTerm); // Debug log
            await fetchAndRenderComments(searchTerm); // Fetch and render comments with the search term
          });
        }

        // Function to fetch and render categories
        async function fetchAndRenderCategories() {
          // Verify supabaseClient is available
          if (!supabaseClient) {
            console.error("Supabase client is not initialized for categories");
            return;
          }

          if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
            console.warn(
              "Supabase URL or Anon Key is not set. Cannot fetch categories."
            );
            return;
          }

          try {
            const { data: categories, error } = await supabaseClient
              .from("categories")
              .select("id, name")
              .order("name", { ascending: true });

            if (error) {
              console.error("Error fetching categories:", error);
              return;
            }

            const categoryListElement = document.getElementById("categoryList");
            if (categoryListElement) {
              categoryListElement.innerHTML = ""; // Clear existing categories
              categories.forEach((category) => {
                const categoryHtml = `
                  <li><a href="index.html#blog-section?category=${encodeURIComponent(
                    category.name
                  )}">${category.name} <span>(0)</span></a></li>
                `; // Placeholder count (0)
                categoryListElement.insertAdjacentHTML(
                  "beforeend",
                  categoryHtml
                );
              });
            }
          } catch (error) {
            console.error(
              "An unexpected error occurred while fetching categories:",
              error
            );
          }
        }

        // Initial fetch and render of categories when the page loads
        fetchAndRenderCategories();
      };
    </script>
