<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Comment System Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .progress { background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 20px 0; }
        .progress-bar { background: #28a745; height: 30px; line-height: 30px; text-align: center; color: white; transition: width 0.3s; }
        .step { margin: 15px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .step.success { border-left-color: #28a745; background: #d4edda; }
        .step.error { border-left-color: #dc3545; background: #f8d7da; }
        .step.running { border-left-color: #ffc107; background: #fff3cd; }
        button { background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px 0; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .result { padding: 10px; margin: 5px 0; border-radius: 4px; font-family: monospace; font-size: 14px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; overflow-x: auto; }
        .manual-steps { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .manual-steps h3 { margin-top: 0; color: #495057; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Automated Comment System Setup</h1>
        <p>This tool will automatically set up your comment system database and fix all issues.</p>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 0%">0%</div>
        </div>

        <button onclick="runFullSetup()" id="setup-btn">🔧 Run Complete Setup</button>
        <button onclick="testSystem()" id="test-btn" disabled>🧪 Test Comment System</button>

        <div id="setup-steps"></div>

        <div class="manual-steps">
            <h3>📋 Manual Setup (if automatic setup fails)</h3>
            <p>If the automatic setup doesn't work, follow these steps in your Supabase dashboard:</p>
            
            <h4>Step 1: Go to SQL Editor in Supabase Dashboard</h4>
            <p>Navigate to your Supabase project → SQL Editor → New Query</p>
            
            <h4>Step 2: Run this SQL code:</h4>
            <pre id="sql-code">-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  website VARCHAR(255),
  message TEXT NOT NULL,
  post_id VARCHAR(255) NOT NULL DEFAULT 'general',
  parent_id INTEGER REFERENCES comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS for public commenting (IMPORTANT!)
ALTER TABLE comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;

-- Grant permissions to anon role
GRANT ALL ON comments TO anon;
GRANT ALL ON categories TO anon;
GRANT USAGE, SELECT ON SEQUENCE comments_id_seq TO anon;
GRANT USAGE, SELECT ON SEQUENCE categories_id_seq TO anon;

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
('Technology', 'Posts about latest technology trends'),
('Web Development', 'Full-stack development tutorials and tips'),
('AI & Machine Learning', 'Artificial Intelligence and ML insights'),
('Serverless', 'Serverless architecture and cloud computing'),
('JavaScript', 'JavaScript programming and frameworks'),
('Career', 'Professional development and career advice')
ON CONFLICT (name) DO NOTHING;

-- Insert sample comments for testing
INSERT INTO comments (name, email, message, post_id) VALUES 
('John Doe', '<EMAIL>', 'Great article! Very informative.', 'general'),
('Jane Smith', '<EMAIL>', 'Thanks for sharing this knowledge.', 'general'),
('Alex Johnson', '<EMAIL>', 'Looking forward to more content like this.', 'general')
ON CONFLICT DO NOTHING;</pre>

            <h4>Step 3: Enable Anonymous Users</h4>
            <p>Go to Authentication → Settings → Enable "Allow anonymous sign-ins"</p>

            <h4>Step 4: Test the System</h4>
            <p>Click the "Test Comment System" button above to verify everything works.</p>
        </div>

        <div id="results"></div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        let supabaseClient;
        let currentStep = 0;
        const totalSteps = 6;

        // Initialize Supabase
        try {
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        } catch (error) {
            console.error('Failed to initialize Supabase:', error);
        }

        function updateProgress(step) {
            const percentage = Math.round((step / totalSteps) * 100);
            const progressBar = document.getElementById('progress-bar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        function addStep(title, status = 'running', message = '') {
            const stepsContainer = document.getElementById('setup-steps');
            const stepDiv = document.createElement('div');
            stepDiv.className = `step ${status}`;
            stepDiv.id = `step-${currentStep}`;
            stepDiv.innerHTML = `
                <h4>${title}</h4>
                <div class="result ${status}">${message}</div>
            `;
            stepsContainer.appendChild(stepDiv);
            return stepDiv;
        }

        function updateStep(stepElement, status, message) {
            stepElement.className = `step ${status}`;
            stepElement.querySelector('.result').className = `result ${status}`;
            stepElement.querySelector('.result').textContent = message;
        }

        async function runFullSetup() {
            document.getElementById('setup-btn').disabled = true;
            document.getElementById('setup-steps').innerHTML = '';
            currentStep = 0;

            // Step 1: Test Connection
            currentStep++;
            updateProgress(currentStep);
            const step1 = addStep('🔗 Testing Supabase Connection', 'running', 'Connecting to Supabase...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: { 'apikey': SUPABASE_ANON_KEY }
                });
                
                if (response.ok || response.status === 404) {
                    updateStep(step1, 'success', '✅ Connection successful!');
                } else {
                    updateStep(step1, 'error', `❌ Connection failed: ${response.status}`);
                    return;
                }
            } catch (error) {
                updateStep(step1, 'error', `❌ Connection error: ${error.message}`);
                return;
            }

            // Step 2: Check Tables
            currentStep++;
            updateProgress(currentStep);
            const step2 = addStep('📊 Checking Database Tables', 'running', 'Checking if tables exist...');
            
            try {
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('count', { count: 'exact', head: true });

                if (error && error.message.includes('does not exist')) {
                    updateStep(step2, 'warning', '⚠️ Tables do not exist - will create them');
                } else if (error) {
                    updateStep(step2, 'error', `❌ Table check failed: ${error.message}`);
                } else {
                    updateStep(step2, 'success', '✅ Tables already exist!');
                }
            } catch (error) {
                updateStep(step2, 'warning', '⚠️ Tables need to be created');
            }

            // Step 3: Create Tables (using direct SQL approach)
            currentStep++;
            updateProgress(currentStep);
            const step3 = addStep('🔧 Creating Database Tables', 'running', 'Creating comments and categories tables...');
            
            // Since we can't use RPC, we'll provide instructions for manual setup
            updateStep(step3, 'warning', '⚠️ Please run the SQL code manually in your Supabase dashboard (see instructions below)');

            // Step 4: Test Anonymous Access
            currentStep++;
            updateProgress(currentStep);
            const step4 = addStep('🔓 Testing Anonymous Access', 'running', 'Testing anonymous user permissions...');
            
            try {
                // Try a simple query to test permissions
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('count', { count: 'exact', head: true });

                if (error && error.code === '42501') {
                    updateStep(step4, 'error', '❌ RLS is blocking access - please disable RLS manually');
                } else if (error && error.message.includes('does not exist')) {
                    updateStep(step4, 'warning', '⚠️ Tables need to be created first');
                } else if (error) {
                    updateStep(step4, 'error', `❌ Access test failed: ${error.message}`);
                } else {
                    updateStep(step4, 'success', '✅ Anonymous access working!');
                }
            } catch (error) {
                updateStep(step4, 'error', `❌ Access test error: ${error.message}`);
            }

            // Step 5: Test Insert
            currentStep++;
            updateProgress(currentStep);
            const step5 = addStep('💾 Testing Comment Insertion', 'running', 'Testing comment insertion...');
            
            try {
                const testComment = {
                    name: 'Setup Test',
                    email: '<EMAIL>',
                    message: 'This is a test comment from the setup process.',
                    post_id: 'setup-test'
                };

                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (error) {
                    updateStep(step5, 'error', `❌ Insert failed: ${error.message}`);
                } else {
                    updateStep(step5, 'success', `✅ Comment inserted successfully! ID: ${data[0].id}`);
                }
            } catch (error) {
                updateStep(step5, 'error', `❌ Insert error: ${error.message}`);
            }

            // Step 6: Final Verification
            currentStep++;
            updateProgress(currentStep);
            const step6 = addStep('✅ Final Verification', 'running', 'Running final system check...');
            
            try {
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('*')
                    .limit(1);

                if (error) {
                    updateStep(step6, 'error', `❌ System not ready: ${error.message}`);
                    document.getElementById('test-btn').disabled = true;
                } else {
                    updateStep(step6, 'success', '✅ System is ready! You can now test the comment system.');
                    document.getElementById('test-btn').disabled = false;
                }
            } catch (error) {
                updateStep(step6, 'error', `❌ Verification error: ${error.message}`);
                document.getElementById('test-btn').disabled = true;
            }

            document.getElementById('setup-btn').disabled = false;
        }

        async function testSystem() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>🧪 Testing Comment System</h3>';
            
            try {
                // Test comment insertion
                const testComment = {
                    name: 'System Test User',
                    email: '<EMAIL>',
                    message: 'This is a test comment to verify the system is working properly.',
                    post_id: 'system-test'
                };

                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (error) {
                    resultsDiv.innerHTML += `<div class="result error">❌ Test failed: ${error.message}</div>`;
                } else {
                    resultsDiv.innerHTML += `<div class="result success">✅ Test successful! Comment ID: ${data[0].id}</div>`;
                    resultsDiv.innerHTML += `<div class="result info">🎉 Your comment system is working perfectly! You can now use it on your website.</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="result error">❌ Test error: ${error.message}</div>`;
            }
        }

        // Auto-start setup
        window.onload = function() {
            setTimeout(() => {
                document.getElementById('setup-btn').click();
            }, 1000);
        };
    </script>
</body>
</html>
