    /*
    Flaticon icon font: Flaticon
    Creation date: 20/03/2019 02:18
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-ideas:before { content: "\f100"; }
.flaticon-flasks:before { content: "\f101"; }
.flaticon-analysis:before { content: "\f102"; }
.flaticon-ux-design:before { content: "\f103"; }
.flaticon-web-design:before { content: "\f104"; }
.flaticon-idea:before { content: "\f105"; }
.flaticon-innovation:before { content: "\f106"; }
    
    $font-Flaticon-ideas: "\f100";
    $font-Flaticon-flasks: "\f101";
    $font-Flaticon-analysis: "\f102";
    $font-Flaticon-ux-design: "\f103";
    $font-Flaticon-web-design: "\f104";
    $font-Flaticon-idea: "\f105";
    $font-Flaticon-innovation: "\f106";