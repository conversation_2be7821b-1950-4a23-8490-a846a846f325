<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .test-box { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .sql-code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>🔧 Quick Fix for 401 Error</h1>
    
    <div class="test-box info">
        <h3>📋 Step 1: Run This SQL in Supabase Dashboard</h3>
        <p>Copy this code and paste it in your Supabase SQL Editor:</p>
        <div class="sql-code">
-- DISABLE ROW LEVEL SECURITY (This fixes the 401 error!)
ALTER TABLE comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;

-- Grant permissions to anonymous users
GRANT ALL ON comments TO anon;
GRANT ALL ON categories TO anon;
GRANT USAGE, SELECT ON SEQUENCE comments_id_seq TO anon;
GRANT USAGE, SELECT ON SEQUENCE categories_id_seq TO anon;

-- Verify the fix
SELECT 'RLS disabled successfully!' as status;
        </div>
        <p><strong>After running the SQL above, click the test button below:</strong></p>
    </div>

    <div class="test-box">
        <h3>🧪 Step 2: Test the Fix</h3>
        <button onclick="testCommentInsertion()">Test Comment System</button>
        <div id="test-result"></div>
    </div>

    <div class="test-box">
        <h3>✅ Step 3: Use Your Comment System</h3>
        <p>Once the test passes, your comment system is ready!</p>
        <button onclick="window.open('single.html', '_blank')">Open Main Comment System</button>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        const supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        async function testCommentInsertion() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="test-box info">Testing comment insertion...</div>';

            try {
                const testComment = {
                    name: 'Fix Test User',
                    email: '<EMAIL>',
                    message: 'This is a test comment to verify the 401 error is fixed!',
                    post_id: 'fix-test'
                };

                console.log('Testing comment insertion:', testComment);

                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (error) {
                    console.error('Test failed:', error);
                    if (error.code === '42501') {
                        resultDiv.innerHTML = `
                            <div class="test-box error">
                                <h4>❌ Test Failed - RLS Still Enabled</h4>
                                <p><strong>Error:</strong> ${error.message}</p>
                                <p><strong>Solution:</strong> Please run the SQL code above in your Supabase dashboard to disable RLS.</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="test-box error">
                                <h4>❌ Test Failed</h4>
                                <p><strong>Error:</strong> ${error.message}</p>
                                <p><strong>Code:</strong> ${error.code}</p>
                            </div>
                        `;
                    }
                } else {
                    console.log('Test successful:', data);
                    resultDiv.innerHTML = `
                        <div class="test-box success">
                            <h4>✅ Test Successful!</h4>
                            <p><strong>Comment inserted successfully!</strong></p>
                            <p><strong>Comment ID:</strong> ${data[0].id}</p>
                            <p><strong>Status:</strong> Your comment system is now working perfectly!</p>
                            <p>🎉 The 401 error is fixed! You can now use your comment system.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `
                    <div class="test-box error">
                        <h4>❌ Test Error</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
