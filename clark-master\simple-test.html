<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simple Comment Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .comment-form {
        background: #f5f5f5;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input,
      textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      button {
        background: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background: #0056b3;
      }
      .comments {
        margin-top: 30px;
      }
      .comment {
        background: white;
        border: 1px solid #ddd;
        padding: 15px;
        margin: 10px 0;
        border-radius: 4px;
      }
      .comment-meta {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 10px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <h1>Simple Comment System Test</h1>

    <div class="comment-form">
      <h3>Leave a Comment</h3>
      <form id="commentForm">
        <div class="form-group">
          <label for="name">Name *</label>
          <input type="text" id="name" name="name" required />
        </div>
        <div class="form-group">
          <label for="email">Email *</label>
          <input type="email" id="email" name="email" required />
        </div>
        <div class="form-group">
          <label for="website">Website</label>
          <input type="url" id="website" name="website" />
        </div>
        <div class="form-group">
          <label for="message">Message *</label>
          <textarea id="message" name="message" rows="4" required></textarea>
        </div>
        <button type="submit">Post Comment</button>
      </form>
      <div id="form-messages"></div>
    </div>

    <div class="comments">
      <h3 id="comment-count">Comments</h3>
      <div id="comments-list"></div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script>
      // Supabase configuration
      const SUPABASE_URL = "https://pghdjokgygdgqrykdypm.supabase.co";
      const SUPABASE_ANON_KEY =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0";

      let supabaseClient;

      // Initialize Supabase
      function initSupabase() {
        try {
          console.log("Initializing Supabase...");
          console.log("window.supabase:", typeof window.supabase);

          if (
            typeof window.supabase !== "undefined" &&
            window.supabase.createClient
          ) {
            supabaseClient = window.supabase.createClient(
              SUPABASE_URL,
              SUPABASE_ANON_KEY
            );
            console.log(
              "Supabase client created successfully:",
              supabaseClient
            );
            return true;
          } else {
            console.error("Supabase library not loaded properly");
            return false;
          }
        } catch (error) {
          console.error("Error initializing Supabase:", error);
          return false;
        }
      }

      // Show message to user
      function showMessage(message, isError = false) {
        const messagesDiv = document.getElementById("form-messages");
        messagesDiv.innerHTML = `<div class="${
          isError ? "error" : "success"
        }">${message}</div>`;
        setTimeout(() => {
          messagesDiv.innerHTML = "";
        }, 5000);
      }

      // Load comments
      async function loadComments() {
        try {
          console.log("Loading comments...");
          const { data, error, count } = await supabaseClient
            .from("comments")
            .select("*", { count: "exact" })
            .eq("post_id", "simple-test")
            .order("created_at", { ascending: false });

          if (error) {
            console.error("Error loading comments:", error);
            showMessage("Error loading comments: " + error.message, true);
            return;
          }

          console.log("Comments loaded:", data);
          document.getElementById("comment-count").textContent = `${
            count || 0
          } Comments`;

          const commentsList = document.getElementById("comments-list");
          if (data && data.length > 0) {
            commentsList.innerHTML = data
              .map(
                (comment) => `
                        <div class="comment">
                            <div class="comment-meta">
                                <strong>${comment.name}</strong> - ${new Date(
                  comment.created_at
                ).toLocaleString()}
                                ${
                                  comment.website
                                    ? `- <a href="${comment.website}" target="_blank">Website</a>`
                                    : ""
                                }
                            </div>
                            <div>${comment.message}</div>
                        </div>
                    `
              )
              .join("");
          } else {
            commentsList.innerHTML =
              "<p>No comments yet. Be the first to comment!</p>";
          }
        } catch (error) {
          console.error("Error in loadComments:", error);
          showMessage("Error loading comments: " + error.message, true);
        }
      }

      // Submit comment
      async function submitComment(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);

        const commentData = {
          name: formData.get("name").trim(),
          email: formData.get("email").trim(),
          website: formData.get("website").trim() || null,
          message: formData.get("message").trim(),
          post_id: "simple-test",
        };

        console.log("Submitting comment:", commentData);

        try {
          const { data, error } = await supabaseClient
            .from("comments")
            .insert([commentData])
            .select();

          if (error) {
            console.error("Error submitting comment:", error);
            showMessage("Error submitting comment: " + error.message, true);
            return;
          }

          console.log("Comment submitted successfully:", data);
          showMessage("Comment posted successfully!");
          form.reset();
          await loadComments();
        } catch (error) {
          console.error("Error in submitComment:", error);
          showMessage("Error submitting comment: " + error.message, true);
        }
      }

      // Initialize when page loads
      window.addEventListener("load", async () => {
        console.log("Page loaded, initializing...");

        if (initSupabase()) {
          await loadComments();
          document
            .getElementById("commentForm")
            .addEventListener("submit", submitComment);
          console.log("Comment system initialized successfully");
        } else {
          showMessage("Failed to initialize comment system", true);
        }
      });
    </script>
  </body>
</html>
